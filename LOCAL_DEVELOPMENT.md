# 本地开发指南

## 环境要求

- Node.js 18+ 
- npm 或 yarn
- Wrangler CLI (已通过 `npm install -g wrangler` 安装)

## 快速开始

### 1. 登录 Cloudflare
```bash
wrangler login
```

### 2. 检查配置
```bash
# 检查当前用户
wrangler whoami

# 列出 KV 命名空间
wrangler kv namespace list
```

### 3. 部署到 Cloudflare（推荐）
由于 Windows 上的本地运行时问题，推荐直接部署到 Cloudflare 进行测试：

```bash
# 部署应用
wrangler deploy

# 部署后会显示访问 URL，例如:
# https://schedule-manager.oldwang.workers.dev
```

### 4. 本地开发（如果运行时正常）
```bash
# 尝试本地开发服务器
npm run dev

# 如果本地运行时有问题，使用远程模式
wrangler dev --remote --port 8787
```

## 本地 KV 存储

在本地开发模式下，Wrangler 会自动创建一个本地的 KV 存储模拟器：

- 数据存储在内存中，重启服务器后数据会丢失
- 完全兼容 Cloudflare KV API
- 无需真实的 Cloudflare 账户即可开发

## 测试 API

### 测试基本连接
```bash
# 测试主页
curl http://localhost:8787

# 测试 API 端点
curl http://localhost:8787/api/schedules
```

### 创建测试数据
```bash
# 创建一个测试日程
curl -X POST http://localhost:8787/api/schedules \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试会议",
    "date": "2024-12-20",
    "time": "14:30",
    "priority": "high",
    "description": "这是一个测试日程"
  }'
```

### 查看所有日程
```bash
curl http://localhost:8787/api/schedules | jq
```

## 开发工作流

### 1. 代码热重载
- 修改 `src/index.js` 后，Wrangler 会自动重新加载
- 浏览器中的更改会立即生效

### 2. 调试
- 使用 `console.log()` 在终端中查看日志
- 错误信息会显示在 Wrangler 控制台中

### 3. 测试脚本
```bash
# 快速测试主页是否正常
npm run test

# 测试 API 是否响应
npm run test:api
```

## 常见问题

### 1. Windows 运行时问题
如果遇到 "access violation in the runtime" 错误：
- **解决方案**: 使用部署到 Cloudflare 的方式进行开发测试
- **原因**: Windows 上的 Wrangler 本地运行时可能需要特定的 Visual C++ 运行库
- **替代方案**: 使用 `wrangler dev --remote` 或直接部署测试

### 2. 网络连接问题
如果 `wrangler dev --remote` 失败：
- 检查网络连接和防火墙设置
- 使用 `wrangler deploy` 直接部署进行测试

### 3. KV 命名空间配置
确保 `wrangler.toml` 中的 KV 命名空间 ID 正确：
```bash
# 列出现有的 KV 命名空间
wrangler kv namespace list

# 如果需要创建新的命名空间
wrangler kv namespace create SCHEDULE_KV
```

### 4. 端口被占用
如果 8787 端口被占用，可以修改 `wrangler.toml` 中的端口：
```toml
[dev]
port = 8788
```

## 部署到 Cloudflare

### 1. 登录 Cloudflare
```bash
wrangler auth login
```

### 2. 创建 KV 命名空间
```bash
# 创建生产环境 KV
wrangler kv:namespace create SCHEDULE_KV

# 创建预览环境 KV  
wrangler kv:namespace create SCHEDULE_KV --preview
```

### 3. 更新 wrangler.toml
将创建的 KV 命名空间 ID 更新到 `wrangler.toml` 中。

### 4. 部署
```bash
# 部署到生产环境
npm run deploy

# 部署到测试环境
npm run deploy:staging
```

## 项目结构

```
cloudflare_schedule_hyw/
├── src/
│   └── index.js          # 主应用代码
├── wrangler.toml         # Wrangler 配置
├── package.json          # 项目配置
└── LOCAL_DEVELOPMENT.md  # 本指南
```

## 下一步

1. 尝试修改 `src/index.js` 中的 HTML 或 API 逻辑
2. 测试不同的 API 端点
3. 添加新功能并测试
4. 准备部署到 Cloudflare Workers
