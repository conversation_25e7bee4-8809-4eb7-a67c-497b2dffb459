# Cloudflare Workers 日程管理系统

一个基于 Cloudflare Workers 和 KV 存储的现代化日程管理应用。

## 🚀 快速开始

### 部署状态
✅ **应用已成功部署到 Cloudflare Workers**

**访问地址**: https://schedule-manager.oldwang.workers.dev

### 功能特性
- 📅 日程的增删改查
- 🔔 到期提醒通知（支持 NotifyX）
- 📱 响应式设计，支持移动端
- ⚡ 基于 Cloudflare Workers，全球边缘计算
- 💾 使用 Cloudflare KV 进行数据持久化

## 🛠️ 本地开发

### 环境要求
- Node.js 18+
- Wrangler CLI 4.20.0+

### 开发步骤

1. **登录 Cloudflare**
   ```bash
   wrangler login
   ```

2. **检查配置**
   ```bash
   wrangler whoami
   wrangler kv namespace list
   ```

3. **部署应用**（推荐的开发方式）
   ```bash
   wrangler deploy
   ```

4. **查看实时日志**
   ```bash
   wrangler tail
   ```

### Windows 本地开发注意事项

由于 Windows 上的 Wrangler 本地运行时可能存在兼容性问题，推荐使用以下开发流程：

1. **直接部署测试**：修改代码后直接 `wrangler deploy` 到 Cloudflare 进行测试
2. **使用远程模式**：`wrangler dev --remote`（如果网络允许）
3. **浏览器开发工具**：在部署的应用中使用浏览器开发工具进行调试

## 📁 项目结构

```
cloudflare_schedule_hyw/
├── src/
│   └── index.js              # 主应用代码（Worker + HTML + API）
├── wrangler.toml             # Wrangler 配置文件
├── package.json              # 项目配置和脚本
├── LOCAL_DEVELOPMENT.md      # 详细开发指南
├── deploy-and-test.sh        # 部署和测试脚本
├── test-api.js              # API 测试脚本
└── README.md                # 本文件
```

## 🔧 配置说明

### KV 存储配置
- **命名空间**: SCHEDULE_KV
- **ID**: 3c11fd5477004e44803c4ecaa3c93924
- **用途**: 存储日程数据

### 环境配置
- **生产环境**: `wrangler deploy`
- **测试环境**: `wrangler deploy --env staging`

## 📋 API 接口

### 获取所有日程
```
GET /api/schedules
```

### 创建新日程
```
POST /api/schedules
Content-Type: application/json

{
  "title": "会议标题",
  "date": "2024-12-25",
  "time": "14:30",
  "priority": "high",
  "description": "会议描述",
  "enableNotification": true
}
```

### 更新日程
```
PUT /api/schedules/{id}
Content-Type: application/json

{
  "title": "更新后的标题",
  "completed": true
}
```

### 删除日程
```
DELETE /api/schedules/{id}
```

### 发送通知
```
POST /api/notify
Content-Type: application/json

{
  "apiKey": "your-notifyx-api-key",
  "scheduleId": "schedule-id"
}
```

## 🔔 通知配置

1. 注册 [NotifyX](https://www.notifyx.cn/) 账户
2. 获取 API Key
3. 在应用中的"通知设置"部分输入 API Key
4. 创建日程时启用"到期提醒"选项

## 🚀 部署命令

```bash
# 部署到生产环境
npm run deploy

# 部署到测试环境  
npm run deploy:staging

# 查看部署状态
wrangler deployments list

# 回滚到上一个版本
wrangler rollback
```

## 🐛 故障排除

### 常见问题

1. **本地运行时错误**
   - 使用 `wrangler deploy` 直接部署测试
   - 或尝试 `wrangler dev --remote`

2. **KV 数据问题**
   ```bash
   # 查看 KV 中的数据
   wrangler kv:key list --binding SCHEDULE_KV
   
   # 获取特定数据
   wrangler kv:key get schedules --binding SCHEDULE_KV
   ```

3. **部署失败**
   - 检查 `wrangler.toml` 配置
   - 确认已登录：`wrangler whoami`
   - 检查 KV 命名空间是否存在

## 📞 支持

如有问题，请查看：
1. [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
2. [Wrangler CLI 文档](https://developers.cloudflare.com/workers/wrangler/)
3. 项目中的 `LOCAL_DEVELOPMENT.md` 详细指南
