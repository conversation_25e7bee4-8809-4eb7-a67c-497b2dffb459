#!/bin/bash

# 部署和测试脚本
echo "🚀 Cloudflare Workers 日程管理系统 - 部署和测试"
echo "================================================"

# 检查是否已登录
echo ""
echo "1. 检查 Cloudflare 登录状态..."
if ! wrangler whoami > /dev/null 2>&1; then
    echo "❌ 未登录到 Cloudflare，请先运行: wrangler login"
    exit 1
fi

echo "✅ 已登录到 Cloudflare"

# 检查 KV 命名空间
echo ""
echo "2. 检查 KV 命名空间..."
wrangler kv namespace list

# 部署应用
echo ""
echo "3. 部署应用到 Cloudflare Workers..."
if wrangler deploy; then
    echo "✅ 部署成功！"
else
    echo "❌ 部署失败"
    exit 1
fi

# 获取部署 URL（从部署输出中提取）
echo ""
echo "4. 应用已部署，可以通过以下方式访问："
echo "   🌐 浏览器: https://schedule-manager.oldwang.workers.dev"
echo "   📱 API: https://schedule-manager.oldwang.workers.dev/api/schedules"

# 基本功能测试
echo ""
echo "5. 进行基本功能测试..."
echo "   测试主页访问..."
if curl -s -f https://schedule-manager.oldwang.workers.dev > /dev/null; then
    echo "   ✅ 主页访问正常"
else
    echo "   ❌ 主页访问失败"
fi

echo "   测试 API 端点..."
if curl -s -f https://schedule-manager.oldwang.workers.dev/api/schedules > /dev/null; then
    echo "   ✅ API 端点正常"
else
    echo "   ❌ API 端点失败"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 下一步操作："
echo "   1. 在浏览器中打开: https://schedule-manager.oldwang.workers.dev"
echo "   2. 测试添加、编辑、删除日程功能"
echo "   3. 配置通知 API Key（如需要）"
echo ""
echo "🛠️  开发工作流："
echo "   - 修改代码后运行: wrangler deploy"
echo "   - 查看日志: wrangler tail"
echo "   - 管理 KV 数据: wrangler kv:key list --binding SCHEDULE_KV"
