{"clientTcpRtt": 1, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "NA", "asn": 25693, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "US", "isEUCountry": false, "region": "California", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "5EjML9zcLYJZV9BQttDmaXlpcMCJ5+3LsMFbdgGMM7Y=", "tlsExportedAuthenticator": {"clientFinished": "b005fd1ace42d2c1c0a4fa7eafe3e8ee83ebc2d6f2d42b5049547f382b35c7fe644b82d3b598d0283ac04b79cb0cad53", "clientHandshake": "cdda616115f9b5df4e4eed23610b984b4b94ebcbbe9bd162e535ff481a9743e96c5b8ce410334360ec7739afb1da4fc8", "serverHandshake": "45f1a23e1b9199c14b795c85618204d17bb6f03c4fe0600760f67d262477928f8f51c179cf4ae6f0f029ef8774c747c7", "serverFinished": "1a16fb4edc7fe39aef3379a91a48f0695d39539033365a8bfe8cf4b4257fea793e35e1cbbe28b88ec4421b03d2fe650d"}, "tlsClientHelloLength": "386", "colo": "LAX", "timezone": "America/Los_Angeles", "longitude": "-118.24368", "latitude": "34.05223", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "90009", "city": "Los Angeles", "tlsVersion": "TLSv1.3", "regionCode": "CA", "asOrganization": "Virtual Machine Solutions LLC", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}