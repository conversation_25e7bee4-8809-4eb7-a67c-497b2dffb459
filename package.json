{"name": "schedule-manager", "version": "1.0.0", "description": "基于Cloudflare Workers的日程管理系统", "main": "src/index.js", "scripts": {"dev": "wrangler dev --port 8787", "dev:remote": "wrangler dev", "start": "npm run dev", "test": "curl -s http://localhost:8787 | head -20", "test:api": "curl -s http://localhost:8787/api/schedules", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "login": "wrangler auth login", "whoami": "wrangler auth whoami", "kv:create": "wrangler kv:namespace create SCHEDULE_KV", "kv:list": "wrangler kv:key list --binding SCHEDULE_KV", "kv:get": "wrangler kv:key get schedules --binding SCHEDULE_KV"}, "keywords": ["cloudflare", "workers", "schedule", "calendar"], "author": "Your Name", "license": "MIT"}