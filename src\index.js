// 日程管理系统主入口
export default {
    async fetch(request, env, ctx) {
      const url = new URL(request.url);
      const path = url.pathname;
  
      // 添加CORS预检处理
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
          }
        });
      }
  
      // 路由处理
      if (path === '/' || path === '/index.html') {
        return new Response(HTML_CONTENT, {
          headers: { 'Content-Type': 'text/html;charset=UTF-8' }
        });
      }

      // PWA Manifest
      if (path === '/manifest.json') {
        return new Response(MANIFEST_JSON, {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Service Worker
      if (path === '/sw.js') {
        return new Response(SERVICE_WORKER_JS, {
          headers: {
            'Content-Type': 'application/javascript',
            'Service-Worker-Allowed': '/'
          }
        });
      }

      // App Icons (placeholder responses - in production, serve actual icon files)
      if (path.startsWith('/icon-') && path.endsWith('.png')) {
        return new Response('', {
          status: 404,
          headers: { 'Content-Type': 'image/png' }
        });
      }
  
      if (path === '/api/schedules') {
        return handleSchedules(request, env);
      }
  
      if (path.startsWith('/api/schedules/')) {
        const id = path.split('/')[3];
        return handleScheduleById(request, env, id);
      }
  
      if (path === '/api/notify') {
        return handleNotification(request, env);
      }
  
      return new Response('Not Found', { status: 404 });
    }
  };
  
  // API处理函数保持不变
  async function handleSchedules(request, env) {
    const method = request.method;
  
    try {
      switch (method) {
        case 'GET':
          return await getSchedules(env);
        case 'POST':
          return await createSchedule(request, env);
        default:
          return new Response('Method Not Allowed', { status: 405 });
      }
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  async function handleScheduleById(request, env, id) {
    const method = request.method;
  
    try {
      switch (method) {
        case 'PUT':
          return await updateSchedule(request, env, id);
        case 'DELETE':
          return await deleteSchedule(env, id);
        default:
          return new Response('Method Not Allowed', { status: 405 });
      }
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  async function getSchedules(env) {
    const schedules = await env.SCHEDULE_KV.get('schedules');
    const data = schedules ? JSON.parse(schedules) : [];
    
    return new Response(JSON.stringify(data), {
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  
  async function createSchedule(request, env) {
    const body = await request.json();
    const { title, description, date, time, priority = 'medium' } = body;
  
    if (!title || !date || !time) {
      return new Response(JSON.stringify({ 
        error: '标题、日期和时间为必填项' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  
    const id = crypto.randomUUID();
    const schedule = {
      id,
      title,
      description: description || '',
      date,
      time,
      priority,
      completed: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  
    const existing = await env.SCHEDULE_KV.get('schedules');
    const schedules = existing ? JSON.parse(existing) : [];
    schedules.push(schedule);
  
    schedules.sort((a, b) => {
      const dateA = new Date(`${a.date} ${a.time}`);
      const dateB = new Date(`${b.date} ${b.time}`);
      return dateA - dateB;
    });
  
    await env.SCHEDULE_KV.put('schedules', JSON.stringify(schedules));
  
    return new Response(JSON.stringify(schedule), {
      status: 201,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  
  async function updateSchedule(request, env, id) {
    const body = await request.json();
    const existing = await env.SCHEDULE_KV.get('schedules');
    const schedules = existing ? JSON.parse(existing) : [];
  
    const index = schedules.findIndex(s => s.id === id);
    if (index === -1) {
      return new Response(JSON.stringify({ error: '日程不存在' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  
    schedules[index] = {
      ...schedules[index],
      ...body,
      updatedAt: new Date().toISOString()
    };
  
    schedules.sort((a, b) => {
      const dateA = new Date(`${a.date} ${a.time}`);
      const dateB = new Date(`${b.date} ${b.time}`);
      return dateA - dateB;
    });
  
    await env.SCHEDULE_KV.put('schedules', JSON.stringify(schedules));
  
    return new Response(JSON.stringify(schedules[index]), {
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  
  async function deleteSchedule(env, id) {
    const existing = await env.SCHEDULE_KV.get('schedules');
    const schedules = existing ? JSON.parse(existing) : [];
  
    const filteredSchedules = schedules.filter(s => s.id !== id);
    
    if (filteredSchedules.length === schedules.length) {
      return new Response(JSON.stringify({ error: '日程不存在' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  
    await env.SCHEDULE_KV.put('schedules', JSON.stringify(filteredSchedules));
  
    return new Response(JSON.stringify({ message: '删除成功' }), {
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  
  // 添加通知处理函数
  async function handleNotification(request, env) {
    if (request.method !== 'POST') {
      return new Response('Method Not Allowed', { status: 405 });
    }
    
    try {
      const body = await request.json();
      const { apiKey, scheduleId } = body;
      
      if (!apiKey || !scheduleId) {
        return new Response(JSON.stringify({ 
          error: 'API Key和日程ID为必填项' 
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      const schedules = await getSchedulesData(env);
      const schedule = schedules.find(s => s.id === scheduleId);
      
      if (!schedule) {
        return new Response(JSON.stringify({ 
          error: '日程不存在' 
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // 构造通知数据
      const notificationData = {
        title: `日程提醒: ${schedule.title}`,
        content: schedule.description || '您的日程时间已到',
        description: `时间: ${schedule.date} ${schedule.time}, 优先级: ${getPriorityText(schedule.priority)}`
      };
      
      // 发送通知
      const notifyResponse = await fetch(`https://www.notifyx.cn/api/v1/send/${apiKey}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(notificationData)
      });
      
      if (!notifyResponse.ok) {
        const errorText = await notifyResponse.text();
        return new Response(JSON.stringify({ 
          error: `通知发送失败: ${errorText}` 
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // 更新日程的通知状态
      schedule.notified = true;
      await updateScheduleData(schedule, env);
      
      return new Response(JSON.stringify({ 
        message: '通知发送成功' 
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
      
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `处理通知请求失败: ${error.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  // 辅助函数 - 获取优先级文本
  function getPriorityText(priority) {
    const map = {high: '高', medium: '中', low: '低'};
    return map[priority] || '中';
  }
  
  // 辅助函数 - 获取日程数据
  async function getSchedulesData(env) {
    const schedules = await env.SCHEDULE_KV.get('schedules');
    return schedules ? JSON.parse(schedules) : [];
  }
  
  // 辅助函数 - 更新单个日程
  async function updateScheduleData(updatedSchedule, env) {
    const schedules = await getSchedulesData(env);
    const index = schedules.findIndex(s => s.id === updatedSchedule.id);
    
    if (index === -1) return false;
    
    schedules[index] = {
      ...schedules[index],
      ...updatedSchedule,
      updatedAt: new Date().toISOString()
    };
    
    await env.SCHEDULE_KV.put('schedules', JSON.stringify(schedules));
    return true;
  }
  
 // ------------ 省略前面的 Worker / API 代码 ------------

// ↓↓↓ 重新给出已修正的 HTML_CONTENT ↓↓↓
const HTML_CONTENT = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1.0">
<title>日程管理系统</title>

  <!-- PWA Meta Tags -->
  <meta name="description" content="一个功能强大的日程管理应用，支持离线使用">
  <meta name="theme-color" content="#667eea">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="日程管理">
  <meta name="msapplication-TileColor" content="#667eea">
  <meta name="msapplication-config" content="/browserconfig.xml">

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="/icon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/icon-16x16.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/icon-180x180.png">
  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#667eea">
<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.header p {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 500;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* 日历组件样式 */
.calendar-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e2e8f0;
}

.calendar-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.calendar-nav {
  display: flex;
  gap: 10px;
}

.nav-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #e2e8f0;
  border-radius: 10px;
  overflow: hidden;
}

.calendar-day-header {
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  padding: 15px 5px;
  text-align: center;
  font-weight: 700;
  font-size: 0.85rem;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calendar-day {
  background: white;
  padding: 12px 8px;
  min-height: 80px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.calendar-day:hover {
  background: #f7fafc;
  transform: scale(1.02);
}

.calendar-day.other-month {
  background: #f8fafc;
  color: #a0aec0;
}

.calendar-day.today {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 700;
}

.calendar-day.has-events {
  background: linear-gradient(135deg, #e6fffa, #b2f5ea);
}

.calendar-day.has-events.today {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.day-number {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.event-indicator {
  width: 6px;
  height: 6px;
  background: #667eea;
  border-radius: 50%;
  margin: 1px;
}

.calendar-day.today .event-indicator {
  background: white;
}

.event-count {
  font-size: 0.7rem;
  background: #667eea;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  margin-top: 2px;
  font-weight: 600;
}

.calendar-day.today .event-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 新的删除按钮样式 */
.delete-button {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: rgb(20, 20, 20);
  border: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.164);
  cursor: pointer;
  transition-duration: .3s;
  overflow: hidden;
  position: relative;
}

.delete-button .svgIcon {
  width: 12px;
  transition-duration: .3s;
}

.delete-button .svgIcon path {
  fill: white;
}

.delete-button:hover {
  width: 100px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(255, 69, 69);
  align-items: center;
}

.delete-button:hover .svgIcon {
  width: 30px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.delete-button::before {
  position: absolute;
  top: -20px;
  content: "Delete";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
}

.delete-button:hover::before {
  font-size: 11px;
  opacity: 1;
  transform: translateY(30px);
  transition-duration: .3s;
}

/* 日历悬停提示样式 */
.calendar-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.85rem;
  max-width: 300px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.calendar-tooltip.show {
  opacity: 1;
  visibility: visible;
}

.calendar-tooltip::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

.calendar-tooltip.below::before {
  top: -5px;
  border-bottom: 5px solid rgba(0, 0, 0, 0.9);
}

.calendar-tooltip.above::before {
  bottom: -5px;
  border-top: 5px solid rgba(0, 0, 0, 0.9);
}

/* 成功提示Toast样式 */
.success-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(72, 187, 120, 0.3);
  z-index: 1001;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.success-toast.show {
  opacity: 1;
  transform: translateX(0);
}

.success-toast::before {
  content: '✓';
  margin-right: 8px;
  font-weight: bold;
}

/* PWA 安装按钮样式 */
.install-button {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.install-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

/* PWA 更新提示样式 */
.update-toast {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  z-index: 1001;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.update-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.update-toast button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.update-toast button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* PWA 离线指示器 */
.offline-indicator {
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff6b6b;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1002;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.offline-indicator.show {
  opacity: 1;
}

.tooltip-date {
  font-weight: 700;
  margin-bottom: 8px;
  color: #667eea;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.tooltip-schedule {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tooltip-time {
  color: #a0aec0;
  font-size: 0.75rem;
  min-width: 40px;
}

.tooltip-title {
  flex: 1;
  font-weight: 500;
}



/* 日程列表区域样式 */
.schedule-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e2e8f0;
}

.schedule-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.add-schedule-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-schedule-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.refresh-btn {
  background: linear-gradient(135deg, #a0aec0, #718096);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-out;
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}



@keyframes slideIn {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e6f0ff;
}

.modal-title {
  font-size: 1.5rem;
  color: #3a7bd5;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* 新的表单样式 */
.modal-content {
  background: #FCEDDA;
  border-radius: 8px;
  padding: 25px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-out;
  position: relative;
}

.form .input-box {
  width: 100%;
  margin-top: 10px;
}

.input-box label {
  color: #000;
  font-size: 0.9rem;
  font-weight: 500;
}

.form :where(.input-box input, .input-box select, .input-box textarea) {
  position: relative;
  height: 35px;
  width: 100%;
  outline: none;
  font-size: 1rem;
  color: #808080;
  margin-top: 5px;
  border: 1px solid #EE4E34;
  border-radius: 6px;
  padding: 0 15px;
  background: #FCEDDA;
  transition: all 0.3s ease;
}

.input-box input:focus,
.input-box select:focus,
.input-box textarea:focus {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  border-color: #EE4E34;
}

.input-box textarea {
  height: 80px;
  resize: vertical;
  padding: 10px 15px;
  font-family: inherit;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
  accent-color: #EE4E34;
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-group label {
  margin: 0;
  font-size: 1rem;
  color: #000;
  font-weight: 500;
  cursor: pointer;
}

.form button {
  height: 40px;
  width: 100%;
  color: #000;
  font-size: 1rem;
  font-weight: 400;
  margin-top: 15px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #EE4E34;
}

.form button:hover {
  background: #EE3E34;
}

.form .btn-secondary {
  background: #ccc;
  color: #333;
}

.form .btn-secondary:hover {
  background: #bbb;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  background-color: #3a7bd5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
}

.btn:hover {
  background-color: #2a6ac5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

#submitBtn {
  width: 100%;
  margin-top: 10px;
}

.date-group {
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.date-group h3 {
  margin: 20px 0 15px 0;
  padding: 12px 20px;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border-radius: 12px;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 700;
  border-left: 4px solid #667eea;
}

.schedule-list {
  margin-top: 20px;
}

.schedule-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.schedule-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.schedule-item:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.schedule-item:hover::before {
  opacity: 1;
}

.schedule-item.completed {
  border-left-color: #48bb78;
  background: rgba(240, 253, 244, 0.9);
  opacity: 0.9;
}

.schedule-item.completed::before {
  background: linear-gradient(90deg, #48bb78, #38a169);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.schedule-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.completed .schedule-title {
  text-decoration: line-through;
  color: #6c757d;
}

.schedule-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.schedule-meta span {
  display: inline-flex;
  align-items: center;
  color: #6c757d;
}



.schedule-description {
  margin: 15px 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  font-size: 0.95rem;
  color: #666;
  white-space: pre-line;
}

.schedule-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.empty-state {
  text-align: center;
  padding: 50px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #3a7bd5;
}

.loading {
  text-align: center;
  padding: 30px;
  color: #6c757d;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    padding: 30px 15px;
    margin-bottom: 25px;
  }

  .header h1 {
    font-size: 2.2rem;
  }

  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .calendar-section, .schedule-section, .notification-settings {
    padding: 20px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .calendar-title {
    font-size: 1.5rem;
  }

  .calendar-day {
    min-height: 60px;
    padding: 8px 4px;
  }

  .day-number {
    font-size: 0.9rem;
  }

  .schedule-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .schedule-header h2 {
    font-size: 1.5rem;
    text-align: center;
  }

  .schedule-actions {
    flex-direction: column;
    gap: 8px;
  }

  .schedule-actions button {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    padding: 20px;
    margin: 10px;
  }

  .add-schedule-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1>📅 日程管理系统</h1>
    <p>高效管理您的时间，让每一天都充实有序</p>
  </div>

  <div class="main-content">
    <!-- 日历组件 -->
    <div class="calendar-section">
      <div class="calendar-header">
        <h2 class="calendar-title" id="calendarTitle">2024年12月</h2>
        <div class="calendar-nav">
          <button class="nav-btn" onclick="previousMonth()">‹ 上月</button>
          <button class="nav-btn" onclick="nextMonth()">下月 ›</button>
        </div>
      </div>
      <div class="calendar-grid" id="calendarGrid">
        <!-- 日历网格将通过JavaScript生成 -->
      </div>
    </div>

    <!-- 日程列表 -->
    <div class="schedule-section">
      <div class="schedule-header">
        <h2>我的日程</h2>
        <div style="display: flex; gap: 10px;">
          <button class="add-schedule-btn" onclick="openScheduleModal()">
            <span>➕</span>
            <span>添加新日程</span>
          </button>
          <button class="refresh-btn" onclick="loadSchedules()">🔄 刷新</button>
        </div>
      </div>
      <div id="scheduleList" class="schedule-list">
        <div class="loading"><p>正在加载日程...</p></div>
      </div>
    </div>
  </div>

  <div id="notificationSettings" class="notification-settings">
    <h3>🔔 通知设置</h3>
    <div class="input-box">
      <label for="notificationApiKey">NotifyX API Key</label>
      <input type="text" id="notificationApiKey" placeholder="输入您的 NotifyX API Key">
    </div>
    <p class="help-text">通知将使用 NotifyX 服务发送，请输入您的 API Key</p>
    <button class="nav-btn" onclick="saveNotificationSettings()">保存设置</button>
  </div>
</div>

<!-- 日程表单模态框 -->
<div id="scheduleModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title" id="modalTitle">添加新日程</h2>
      <button class="close-btn" onclick="closeScheduleModal()">&times;</button>
    </div>

    <form id="scheduleForm" class="form">
      <div class="input-box">
        <label for="title">标题 *</label>
        <input type="text" id="title" name="title" required placeholder="输入日程标题">
      </div>

      <div class="input-box">
        <label for="date">日期 *</label>
        <input type="date" id="date" name="date" required>
      </div>

      <div class="input-box">
        <label for="time">时间 *</label>
        <input type="time" id="time" name="time" required>
      </div>



      <div class="input-box">
        <label for="description">描述</label>
        <textarea id="description" name="description" placeholder="添加详细描述（可选）"></textarea>
      </div>

      <div class="checkbox-group">
        <input type="checkbox" id="enableNotification" name="enableNotification" checked>
        <label for="enableNotification">启用到期提醒</label>
      </div>

      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button type="button" class="btn btn-secondary" onclick="closeScheduleModal()">取消</button>
        <button type="submit" class="btn" id="submitBtn">
          <span id="submitText">添加日程</span>
        </button>
      </div>
    </form>
  </div>
</div>

<!-- ========= 前端脚本，所有内部模板反引号全部转义 ========= -->
<script>
let schedules = [];
let editingId = null;
let currentDate = new Date();
let selectedDate = null;

document.addEventListener('DOMContentLoaded', ()=>{
  initializePage();
  loadSchedules();
  initNotificationCheck();
  initCalendar();
  initPWA();
});

function initializePage(){
  document.getElementById('date').value = new Date().toISOString().split('T')[0];
  document.getElementById('scheduleForm').addEventListener('submit',handleFormSubmit);

  // 加载通知设置
  const apiKey = localStorage.getItem('notificationApiKey');
  if (apiKey) {
    document.getElementById('notificationApiKey').value = apiKey;
  }

  // 初始化模态框事件
  initModalEvents();

  // 初始化全局事件监听
  initGlobalEvents();
}

// 初始化日历
function initCalendar() {
  currentDate = new Date();
  renderCalendar();
}

// 渲染日历
function renderCalendar() {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  // 更新标题
  const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                     '7月', '8月', '9月', '10月', '11月', '12月'];
  document.getElementById('calendarTitle').textContent = \`\${year}年\${monthNames[month]}\`;

  // 获取月份信息
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - firstDay.getDay());

  const calendarGrid = document.getElementById('calendarGrid');
  calendarGrid.innerHTML = '';

  // 添加星期标题
  const dayHeaders = ['日', '一', '二', '三', '四', '五', '六'];
  dayHeaders.forEach(day => {
    const dayHeader = document.createElement('div');
    dayHeader.className = 'calendar-day-header';
    dayHeader.textContent = day;
    calendarGrid.appendChild(dayHeader);
  });

  // 生成日历天数
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0];

  for (let i = 0; i < 42; i++) {
    const cellDate = new Date(startDate);
    cellDate.setDate(startDate.getDate() + i);

    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';

    // 修复日期格式化 - 使用本地日期而不是UTC日期
    const year = cellDate.getFullYear();
    const month_num = cellDate.getMonth() + 1;
    const day = cellDate.getDate();
    const dateStr = \`\${year}-\${month_num.toString().padStart(2, '0')}-\${day.toString().padStart(2, '0')}\`;

    const isCurrentMonth = cellDate.getMonth() === month;
    const isToday = dateStr === todayStr;

    if (!isCurrentMonth) {
      dayElement.classList.add('other-month');
    }
    if (isToday) {
      dayElement.classList.add('today');
    }

    // 检查是否有日程
    const daySchedules = schedules.filter(s => s.date === dateStr);
    if (daySchedules.length > 0) {
      dayElement.classList.add('has-events');
    }

    // 日期数字
    const dayNumber = document.createElement('div');
    dayNumber.className = 'day-number';
    dayNumber.textContent = cellDate.getDate();
    dayElement.appendChild(dayNumber);

    // 事件指示器
    if (daySchedules.length > 0) {
      if (daySchedules.length <= 3) {
        daySchedules.forEach(() => {
          const indicator = document.createElement('div');
          indicator.className = 'event-indicator';
          dayElement.appendChild(indicator);
        });
      } else {
        const eventCount = document.createElement('div');
        eventCount.className = 'event-count';
        eventCount.textContent = daySchedules.length;
        dayElement.appendChild(eventCount);
      }
    }

    // 悬停事件（显示日程详情）
    if (daySchedules.length > 0) {
      dayElement.addEventListener('mouseenter', (e) => showTooltip(e, dateStr, daySchedules));
      dayElement.addEventListener('mouseleave', hideTooltip);
    }

    // 右键事件（快速添加）
    dayElement.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      quickAddSchedule(dateStr);
    });

    // 移动端点击事件（替代悬停）
    dayElement.addEventListener('click', (e) => {
      if (window.innerWidth <= 768 && daySchedules.length > 0) {
        showMobileScheduleDetails(dateStr, daySchedules);
      }
    });

    calendarGrid.appendChild(dayElement);
  }
}

// 显示悬停提示
let tooltipTimeout;
function showTooltip(event, dateStr, daySchedules) {
  clearTimeout(tooltipTimeout);

  tooltipTimeout = setTimeout(() => {
    hideTooltip(); // 先隐藏已存在的提示

    const tooltip = document.createElement('div');
    tooltip.className = 'calendar-tooltip';
    tooltip.id = 'calendar-tooltip';

    // 格式化日期
    const formattedDate = formatDate(dateStr);

    let tooltipContent = \`<div class="tooltip-date">\${formattedDate}</div>\`;

    daySchedules.forEach(schedule => {
      tooltipContent += \`
        <div class="tooltip-schedule">
          <span class="tooltip-time">\${schedule.time}</span>
          <span class="tooltip-title">\${schedule.title}</span>
        </div>
      \`;
    });

    tooltip.innerHTML = tooltipContent;
    document.body.appendChild(tooltip);

    // 定位提示框 - 修复定位逻辑
    const rect = event.target.getBoundingClientRect();

    // 先设置位置为屏幕外以获取正确的尺寸
    tooltip.style.position = 'fixed';
    tooltip.style.left = '-9999px';
    tooltip.style.top = '-9999px';
    tooltip.style.visibility = 'hidden';

    // 获取tooltip的实际尺寸
    const tooltipRect = tooltip.getBoundingClientRect();

    // 水平居中对齐到目标日期单元格
    let left = rect.left + rect.width / 2 - tooltipRect.width / 2;

    // 默认显示在日期单元格下方，留10px间距
    let top = rect.bottom + window.scrollY + 10;
    let showBelow = true;

    // 检查下方是否有足够空间
    if (rect.bottom + tooltipRect.height + 10 > window.innerHeight) {
      // 下方空间不足，显示在上方
      top = rect.top + window.scrollY - tooltipRect.height - 10;
      showBelow = false;
    }

    // 水平边界检查
    if (left < 10) {
      left = 10;
    } else if (left + tooltipRect.width > window.innerWidth - 10) {
      left = window.innerWidth - tooltipRect.width - 10;
    }

    // 垂直边界检查（如果上方也没有空间，则强制显示在下方）
    if (!showBelow && rect.top - tooltipRect.height - 10 < 0) {
      top = rect.bottom + window.scrollY + 10;
      showBelow = true;
    }

    // 恢复可见性
    tooltip.style.visibility = 'visible';

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';

    // 添加位置类以正确显示箭头
    tooltip.classList.add(showBelow ? 'below' : 'above');

    // 显示动画
    setTimeout(() => tooltip.classList.add('show'), 10);
  }, 300); // 300ms 延迟
}

// 隐藏悬停提示
function hideTooltip() {
  clearTimeout(tooltipTimeout);
  const tooltip = document.getElementById('calendar-tooltip');
  if (tooltip) {
    tooltip.classList.remove('show');
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  }
}

// 移动端显示日程详情
function showMobileScheduleDetails(dateStr, daySchedules) {
  const formattedDate = formatDate(dateStr);
  const scheduleList = daySchedules.map(s => {
    return \`• \${s.time} - \${s.title}\`;
  }).join('\\n');

  alert(\`\${formattedDate} 的日程：\\n\\n\${scheduleList}\`);
}

// 显示成功提示Toast
function showSuccessToast(message) {
  // 移除已存在的toast
  const existingToast = document.getElementById('success-toast');
  if (existingToast) {
    existingToast.remove();
  }

  // 创建新的toast
  const toast = document.createElement('div');
  toast.id = 'success-toast';
  toast.className = 'success-toast';
  toast.textContent = message;

  document.body.appendChild(toast);

  // 显示动画
  setTimeout(() => toast.classList.add('show'), 10);

  // 3秒后自动隐藏
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

// PWA 相关功能
let deferredPrompt;
let isInstalled = false;

// 初始化 PWA 功能
function initPWA() {
  // 注册 Service Worker
  if ('serviceWorker' in navigator) {
    registerServiceWorker();
  }

  // 监听安装提示事件
  window.addEventListener('beforeinstallprompt', (e) => {
    console.log('PWA: Install prompt available');
    e.preventDefault();
    deferredPrompt = e;
    showInstallButton();
  });

  // 监听应用安装事件
  window.addEventListener('appinstalled', (e) => {
    console.log('PWA: App installed');
    isInstalled = true;
    hideInstallButton();
    showSuccessToast('应用已成功安装到设备');
  });

  // 检查是否已安装
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    isInstalled = true;
    console.log('PWA: Running in standalone mode');
  }

  // 处理 URL 参数（快捷方式）
  handleShortcuts();
}

// 注册 Service Worker
async function registerServiceWorker() {
  try {
    const registration = await navigator.serviceWorker.register('/sw.js');
    console.log('PWA: Service Worker registered', registration.scope);

    // 监听更新
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          showUpdateAvailable();
        }
      });
    });
  } catch (error) {
    console.error('PWA: Service Worker registration failed', error);
  }
}

// 显示安装按钮
function showInstallButton() {
  if (isInstalled) return;

  const installButton = document.createElement('button');
  installButton.id = 'install-button';
  installButton.className = 'install-button';
  installButton.innerHTML = '📱 安装应用';
  installButton.onclick = installApp;

  // 添加到页面顶部
  const header = document.querySelector('.header');
  if (header && !document.getElementById('install-button')) {
    header.appendChild(installButton);
  }
}

// 隐藏安装按钮
function hideInstallButton() {
  const installButton = document.getElementById('install-button');
  if (installButton) {
    installButton.remove();
  }
}

// 安装应用
async function installApp() {
  if (!deferredPrompt) return;

  try {
    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;

    if (outcome === 'accepted') {
      console.log('PWA: User accepted install prompt');
    } else {
      console.log('PWA: User dismissed install prompt');
    }

    deferredPrompt = null;
    hideInstallButton();
  } catch (error) {
    console.error('PWA: Install failed', error);
  }
}

// 显示更新可用提示
function showUpdateAvailable() {
  const updateToast = document.createElement('div');
  updateToast.className = 'update-toast';
  updateToast.innerHTML = \`
    <span>新版本可用</span>
    <button onclick="updateApp()">更新</button>
    <button onclick="this.parentElement.remove()">稍后</button>
  \`;

  document.body.appendChild(updateToast);
  setTimeout(() => updateToast.classList.add('show'), 10);
}

// 更新应用
function updateApp() {
  window.location.reload();
}

// 处理快捷方式
function handleShortcuts() {
  const urlParams = new URLSearchParams(window.location.search);
  const action = urlParams.get('action');

  switch (action) {
    case 'add':
      setTimeout(() => openScheduleModal(), 500);
      break;
    case 'today':
      setTimeout(() => {
        const today = new Date().toISOString().split('T')[0];
        const todayGroup = document.querySelector(\`[data-date="\${today}"]\`);
        if (todayGroup) {
          todayGroup.scrollIntoView({ behavior: 'smooth' });
        }
      }, 500);
      break;
  }
}

// 快速添加日程
function quickAddSchedule(dateStr) {
  document.getElementById('date').value = dateStr;
  openScheduleModal();
}

// 上一月
function previousMonth() {
  currentDate.setMonth(currentDate.getMonth() - 1);
  renderCalendar();
}

// 下一月
function nextMonth() {
  currentDate.setMonth(currentDate.getMonth() + 1);
  renderCalendar();
}

// 格式化日期显示
function formatDate(dateStr) {
  // 修复日期解析 - 确保使用本地时区
  const [year, month, day] = dateStr.split('-').map(Number);
  const date = new Date(year, month - 1, day);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
}

// 初始化模态框事件
function initModalEvents() {
  const modal = document.getElementById('scheduleModal');

  // 点击遮罩层关闭模态框
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeScheduleModal();
    }
  });

  // ESC键关闭模态框
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      if (modal.classList.contains('show')) {
        closeScheduleModal();
      }
    }
  });
}

// 初始化全局事件
function initGlobalEvents() {
  // 点击页面其他地方时隐藏tooltip
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.calendar-day')) {
      hideTooltip();
    }
  });

  // 滚动时隐藏tooltip
  document.addEventListener('scroll', hideTooltip);

  // 窗口大小改变时隐藏tooltip
  window.addEventListener('resize', hideTooltip);
}

// 打开日程模态框
function openScheduleModal(isEdit = false) {
  const modal = document.getElementById('scheduleModal');
  const modalTitle = document.getElementById('modalTitle');
  const submitText = document.getElementById('submitText');

  if (isEdit) {
    modalTitle.textContent = '编辑日程';
    submitText.textContent = '更新日程';
  } else {
    modalTitle.textContent = '添加新日程';
    submitText.textContent = '添加日程';
    // 重置表单
    document.getElementById('scheduleForm').reset();
    document.getElementById('date').value = new Date().toISOString().split('T')[0];
    document.getElementById('enableNotification').checked = true;
    editingId = null;
  }

  modal.classList.add('show');
  // 聚焦到标题输入框
  setTimeout(() => {
    document.getElementById('title').focus();
  }, 300);
}

// 关闭日程模态框
function closeScheduleModal() {
  const modal = document.getElementById('scheduleModal');
  modal.classList.remove('show');

  // 重置编辑状态
  editingId = null;
  document.getElementById('scheduleForm').reset();
  document.getElementById('submitText').textContent = '添加日程';
  document.getElementById('modalTitle').textContent = '添加新日程';
}

// 初始化通知检查
function initNotificationCheck() {
  // 每分钟检查一次是否有需要通知的日程
  setInterval(checkScheduleNotifications, 60000);
  // 页面加载时立即检查一次
  checkScheduleNotifications();
}

// 检查日程通知
async function checkScheduleNotifications() {
  const apiKey = localStorage.getItem('notificationApiKey');
  if (!apiKey) return; // 没有设置API Key，不发送通知
  
  if (!schedules || !schedules.length) return;
  
  const now = new Date();
  const currentDate = now.toISOString().split('T')[0];
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  
  // 检查所有今天的未完成日程
  schedules.forEach(schedule => {
    if (schedule.completed) return; // 已完成的不通知
    if (schedule.notified) return; // 已通知的不重复通知
    
    if (schedule.date === currentDate) {
      const [scheduleHour, scheduleMinute] = schedule.time.split(':').map(Number);
      
      // 如果时间相同或刚过（5分钟内），发送通知
      const timeDiffMinutes = (currentHour - scheduleHour) * 60 + (currentMinute - scheduleMinute);
      if (timeDiffMinutes >= 0 && timeDiffMinutes <= 5) {
        sendNotification(schedule);
        // 标记为已通知，避免重复通知
        markScheduleNotified(schedule.id);
      }
    }
  });
}

// 标记日程已通知
async function markScheduleNotified(id) {
  try {
    const schedule = schedules.find(s => s.id === id);
    if (!schedule) return;
    
    schedule.notified = true;
    
    await fetch(\`/api/schedules/\${id}\`, {
      method: 'PUT',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({ notified: true })
    });
  } catch (err) {
    console.error('标记通知状态失败:', err);
  }
}

// 修改发送通知的函数
async function sendNotification(schedule) {
  const apiKey = localStorage.getItem('notificationApiKey');
  if (!apiKey || !schedule.enableNotification) return;
  
  try {
    // 使用服务端API发送通知
    const response = await fetch('/api/notify', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({
        apiKey: apiKey,
        scheduleId: schedule.id
      })
    });
    
    if (response.ok) {
      console.log('通知发送成功:', schedule.title);
      // 更新本地状态
      const index = schedules.findIndex(s => s.id === schedule.id);
      if (index !== -1) {
        schedules[index].notified = true;
      }
    } else {
      const errorData = await response.json();
      console.error('通知发送失败:', errorData.error);
    }
  } catch (err) {
    console.error('通知发送异常:', err);
  }
}

// 手动发送通知
async function manualSendNotification(id) {
  const schedule = schedules.find(s => s.id === id);
  if (!schedule) return;
  
  await sendNotification(schedule);
  alert('通知已发送');
}

function getPriorityText(priority) {
  const map = {high: '高', medium: '中', low: '低'};
  return map[priority] || '中';
}

// 保存通知设置
function saveNotificationSettings() {
  const apiKey = document.getElementById('notificationApiKey').value.trim();
  if (apiKey) {
    localStorage.setItem('notificationApiKey', apiKey);
    alert('通知设置已保存');
  } else {
    localStorage.removeItem('notificationApiKey');
    alert('通知已禁用');
  }
}

// 表单提交（新增 / 更新）
async function handleFormSubmit(e){
  e.preventDefault();
  const btn=document.getElementById('submitBtn');
  const txt=document.getElementById('submitText');
  const origin=txt.textContent;
  btn.disabled=true;
  txt.textContent=editingId?'更新中...':'添加中...';

  try{
    const fd=new FormData(e.target);
    const data={
      title:fd.get('title'),
      date:fd.get('date'),
      time:fd.get('time'),
      description:fd.get('description'),
      enableNotification: !!document.getElementById('enableNotification').checked,
      notified: false // 初始状态为未通知
    };
    const url=editingId? \`/api/schedules/\${editingId}\` : '/api/schedules';
    const method=editingId?'PUT':'POST';

    const resp=await fetch(url,{method,
      headers:{'Content-Type':'application/json'},
      body:JSON.stringify(data)});
    if(!resp.ok){alert('操作失败:'+ (await resp.text()));return;}

    // 成功后关闭模态框并刷新列表
    closeScheduleModal();
    await loadSchedules();

    // 显示成功提示（非侵入式）
    showSuccessToast(editingId ? '日程更新成功' : '日程添加成功');

  }catch(err){
    console.error(err);
    alert('网络错误');
  }finally{
    btn.disabled=false;
    txt.textContent=origin;
  }
}

// 加载日程
async function loadSchedules(){
  try{
    const res=await fetch('/api/schedules');
    schedules=await res.json();
    renderSchedules();
    renderCalendar(); // 更新日历显示
  }catch(e){
    console.error(e);
    document.getElementById('scheduleList').innerHTML=
      '<div class="loading"><p>加载失败，请刷新重试</p></div>';
  }
}

// 渲染
function renderSchedules(){
  const box=document.getElementById('scheduleList');
  if(!schedules.length){
    box.innerHTML=
      '<div class="empty-state">\\\n' +
      '  <h3>📝 暂无日程</h3>\\\n' +
      '  <p>添加您的第一个日程安排吧！</p>\\\n' +
      '</div>';
    return;
  }
  const today=(new Date()).toISOString().split('T')[0];
  const grouped={};
  schedules.forEach(s=>{(grouped[s.date]=grouped[s.date]||[]).push(s);});
  Object.keys(grouped).forEach(d=>grouped[d].sort((a,b)=>a.time.localeCompare(b.time)));

  let html='';
  for(const date of Object.keys(grouped).sort()){
    const isToday=date===today;
    const isPast=date<today;
    html+=
      '<div class="date-group" data-date="'+date+'">\\\n'+
      '  <h3>'+ formatDateHeader(date,isToday,isPast) +'</h3>\\\n';

    grouped[date].forEach(s=>{
      html+= renderScheduleItem(s, isPast && !s.completed);
    });
    html+='</div>';
  }
  box.innerHTML=html;
}

// 日期标题
function formatDateHeader(date,isToday,isPast){
  const opts={month:'long',day:'numeric',weekday:'long'};
  // 修复日期解析 - 使用本地时区
  const [year, month, day] = date.split('-').map(Number);
  const dateObj = new Date(year, month - 1, day);
  const text = dateObj.toLocaleDateString('zh-CN',opts);
  if(isToday) return '🌟 今天 - '+text;
  if(isPast)  return '⏰ '+text+' (已过期)';
  return '📅 '+text;
}

// 单条
function renderScheduleItem(s, overdue){
  const doneClass=s.completed?'completed':'';
  const border=overdue?'border-left:4px solid #dc3545;':'';

  const notifyButton = s.enableNotification && !s.notified && !s.completed ?
    '<button class="btn btn-secondary btn-small tooltip" data-tooltip="立即发送通知" onclick="manualSendNotification(\\\''+s.id+'\\\')">🔔 提醒</button>\\\n' : '';

  return (
    '<div class="schedule-item '+doneClass+'" style="'+border+'">\\\n'+
    ' <div class="schedule-header">\\\n'+
    '   <div>\\\n'+
    '     <div class="schedule-title">'+esc(s.title)+'</div>\\\n'+
    '     <div class="schedule-meta">\\\n'+
    '       <span>⏰ '+s.time+'</span>\\\n'+
    (overdue? '       <span style="color:#dc3545;font-weight:600;">⚠️ 已逾期</span>\\\n':'')+
    (s.notified? '    <span class="notification-badge">🔔 已通知</span>\\\n':'')+
    '     </div>\\\n'+
    '   </div>\\\n'+
    ' </div>\\\n'+
    (s.description?
      ' <div class="schedule-description">'+esc(s.description)+'</div>\\\n':'')+
    ' <div class="schedule-actions">\\\n'+
    '   <button class="btn btn-small '+(s.completed?'btn-secondary':'')+'" onclick="toggleComplete(\\\''+s.id+'\\\')">'+
            (s.completed?'↩️ 取消完成':'✅ 标记完成')+'</button>\\\n'+
    notifyButton+
    '   <button class="btn btn-secondary btn-small" onclick="editSchedule(\\\''+s.id+'\\\')">✏️ 编辑</button>\\\n'+
    '   <button class="delete-button" onclick="deleteSchedule(\\\''+s.id+'\\\')" title="删除日程">\\\n'+
    '     <svg class="svgIcon" viewBox="0 0 448 512">\\\n'+
    '       <path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"></path>\\\n'+
    '     </svg>\\\n'+
    '   </button>\\\n'+
    ' </div>\\\n'+
    '</div>'
  );
}

// HTML 转义
function esc(t){const d=document.createElement('div');d.textContent=t;return d.innerHTML;}

// 切换完成
async function toggleComplete(id){
  const s=schedules.find(x=>x.id===id); if(!s)return;
  await fetch('/api/schedules/'+id,{method:'PUT',
        headers:{'Content-Type':'application/json'},
        body:JSON.stringify({...s,completed:!s.completed})});
  loadSchedules();
}

// 编辑
function editSchedule(id){
  const s=schedules.find(x=>x.id===id); if(!s)return;

  // 填充表单数据
  document.getElementById('title').value=s.title;
  document.getElementById('date').value=s.date;
  document.getElementById('time').value=s.time;
  document.getElementById('description').value=s.description||'';
  document.getElementById('enableNotification').checked = s.enableNotification !== false;

  // 设置编辑状态
  editingId=id;

  // 打开模态框（编辑模式）
  openScheduleModal(true);
}

// 删除
async function deleteSchedule(id){
  const s=schedules.find(x=>x.id===id); if(!s)return;
  if(!confirm('确定删除"'+s.title+'"吗？'))return;
  await fetch('/api/schedules/'+id,{method:'DELETE'});
  loadSchedules();
}

// 快捷键处理
document.addEventListener('keydown',e=>{
  if(e.ctrlKey&&e.key==='r'){e.preventDefault();loadSchedules();}
});

// 自动刷新
setInterval(loadSchedules,30000);
</script>
<style>
/* 通知设置样式 */
.notification-settings {
  margin-top: 30px;
  background: #FCEDDA;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.notification-settings h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #000;
  font-weight: 600;
  text-align: center;
}

.notification-settings .input-box input {
  height: 35px;
  width: 100%;
  outline: none;
  font-size: 1rem;
  color: #808080;
  margin-top: 5px;
  border: 1px solid #EE4E34;
  border-radius: 6px;
  padding: 0 15px;
  background: #FCEDDA;
}

.notification-settings .input-box input:focus {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  border-color: #EE4E34;
}

.help-text {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 5px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  width: auto;
  margin-right: 8px;
}

/* 确保复选框保持正常大小 */
input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.notification-badge {
  background-color: #e3f2fd;
  color: #0d6efd !important;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
}
</style>
</body></html>`;
