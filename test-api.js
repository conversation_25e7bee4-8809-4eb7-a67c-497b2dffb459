// API 测试脚本
// 使用方法: node test-api.js [base-url]
// 例如: node test-api.js https://schedule-manager.oldwang.workers.dev

const baseUrl = process.argv[2] || 'http://localhost:8787';

console.log(`🧪 测试 API: ${baseUrl}`);

async function testAPI() {
  try {
    // 测试 1: 获取主页
    console.log('\n1. 测试主页...');
    const homeResponse = await fetch(baseUrl);
    console.log(`   状态: ${homeResponse.status}`);
    console.log(`   内容类型: ${homeResponse.headers.get('content-type')}`);
    
    // 测试 2: 获取所有日程
    console.log('\n2. 测试获取日程列表...');
    const schedulesResponse = await fetch(`${baseUrl}/api/schedules`);
    console.log(`   状态: ${schedulesResponse.status}`);
    const schedules = await schedulesResponse.json();
    console.log(`   现有日程数量: ${schedules.length}`);
    
    // 测试 3: 创建新日程
    console.log('\n3. 测试创建新日程...');
    const newSchedule = {
      title: '测试会议',
      date: '2024-12-25',
      time: '14:30',
      priority: 'high',
      description: '这是一个API测试创建的日程',
      enableNotification: true
    };
    
    const createResponse = await fetch(`${baseUrl}/api/schedules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newSchedule)
    });
    
    console.log(`   状态: ${createResponse.status}`);
    const createdSchedule = await createResponse.json();
    console.log(`   创建的日程ID: ${createdSchedule.id}`);
    
    // 测试 4: 更新日程
    console.log('\n4. 测试更新日程...');
    const updateData = {
      title: '更新后的测试会议',
      completed: true
    };
    
    const updateResponse = await fetch(`${baseUrl}/api/schedules/${createdSchedule.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updateData)
    });
    
    console.log(`   状态: ${updateResponse.status}`);
    const updatedSchedule = await updateResponse.json();
    console.log(`   更新后标题: ${updatedSchedule.title}`);
    console.log(`   完成状态: ${updatedSchedule.completed}`);
    
    // 测试 5: 删除日程
    console.log('\n5. 测试删除日程...');
    const deleteResponse = await fetch(`${baseUrl}/api/schedules/${createdSchedule.id}`, {
      method: 'DELETE'
    });
    
    console.log(`   状态: ${deleteResponse.status}`);
    const deleteResult = await deleteResponse.json();
    console.log(`   删除结果: ${deleteResult.message}`);
    
    // 测试 6: 验证删除
    console.log('\n6. 验证删除结果...');
    const finalSchedulesResponse = await fetch(`${baseUrl}/api/schedules`);
    const finalSchedules = await finalSchedulesResponse.json();
    console.log(`   最终日程数量: ${finalSchedules.length}`);
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
  }
}

// 运行测试
testAPI();
