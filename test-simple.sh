#!/bin/bash

# 简单的 API 测试脚本
BASE_URL=${1:-"https://schedule-manager.oldwang.workers.dev"}

echo "🧪 测试 API: $BASE_URL"

echo ""
echo "1. 测试主页..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$BASE_URL"

echo ""
echo "2. 测试获取日程列表..."
curl -s -w "状态码: %{http_code}\n" "$BASE_URL/api/schedules" | head -5

echo ""
echo "3. 测试创建新日程..."
curl -s -X POST "$BASE_URL/api/schedules" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试会议",
    "date": "2024-12-25", 
    "time": "14:30",
    "priority": "high",
    "description": "API测试创建的日程"
  }' | head -5

echo ""
echo "✅ 基本测试完成！"
echo "🌐 在浏览器中访问: $BASE_URL"
