name = "schedule-manager"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 本地开发配置
[dev]
port = 8787

# KV存储配置
[[kv_namespaces]]
binding = "SCHEDULE_KV"
id = "3c11fd5477004e44803c4ecaa3c93924"
preview_id = "3c11fd5477004e44803c4ecaa3c93924"

# Workers配置
[env.production]
name = "schedule-manager-prod"
[[env.production.kv_namespaces]]
binding = "SCHEDULE_KV"
id = "3c11fd5477004e44803c4ecaa3c93924"
preview_id = "3c11fd5477004e44803c4ecaa3c93924"

[env.staging]
name = "schedule-manager-staging"
[[env.staging.kv_namespaces]]
binding = "SCHEDULE_KV"
id = "3c11fd5477004e44803c4ecaa3c93924"
preview_id = "3c11fd5477004e44803c4ecaa3c93924"

# 本地开发环境
[env.development]
name = "schedule-manager-dev"
[[env.development.kv_namespaces]]
binding = "SCHEDULE_KV"
id = "local-schedule-kv"
preview_id = "local-schedule-kv"
